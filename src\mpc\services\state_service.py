import logging
import queue
from datetime import datetime
from multiprocessing import Queue
from multiprocessing.connection import Connection
from threading import Event, Thread
from typing import Any, Optional

import numpy as np
import pandas as pd

from config.configs import Configs, TrainerConfig
from config.logger import Logger
from consts import LogFileNameEnum as LFN
from database.database import get_db_manager, init_database
from track import StripTracker
from track.track import Strip, TrackingMsg
from utils import timer


class StateService:
    """
    系统状态管理服务
    """

    def __init__(self, input_queue: Queue, pipe_conn: Connection):
        Configs.initialize()
        Logger.initialize(file_name=LFN.MPC_STATE.value)

        self.logger = logging.getLogger(self.__class__.__name__)
        self.input_queue = input_queue
        self.pipe_conn = pipe_conn

        # 初始化数据库
        init_database(Configs.get_db_config())
        self.dbm = get_db_manager()
        self.dbm.create_tables()

        self.strip_tracker = StripTracker(online_mode=True, work_dir="")

    def start(self):
        self.logger.info("状态服务启动中...")

        stop_event = Event()
        queue_thread = Thread(target=self.handle_queue, args=(stop_event,))
        pipe_thread = Thread(target=self.handle_pipe, args=(stop_event,))

        try:
            queue_thread.start()
            pipe_thread.start()

            # 主线程等待工作线程结束
            while queue_thread.is_alive() or pipe_thread.is_alive():
                queue_thread.join(timeout=1)
                pipe_thread.join(timeout=1)

        except KeyboardInterrupt:
            self.logger.warning("收到中断信号，正在停止服务...")
            stop_event.set()
        except Exception as ex:
            self.logger.error(f"状态服务异常: {ex}")
            stop_event.set()
        finally:
            # 确保设置停止事件
            stop_event.set()
            # 等待线程结束
            queue_thread.join(timeout=1.0)
            pipe_thread.join(timeout=1.0)

            self.logger.info("状态服务已停止.")

    def handle_queue(self, stop_event: Event):
        self.logger.info("队列处理线程启动")
        while not stop_event.is_set():
            try:
                record = self.input_queue.get(timeout=0.5)
                record_tracked = self._strip_track_online(record)

                if record_tracked is None:
                    self.logger.warning("跟踪失败...")
                    continue

                self.logger.info(f"跟踪结果: {record_tracked}")
                # TODO: 状态管理

            except queue.Empty:  # 处理队列空异常
                continue
            except Exception as ex:
                self.logger.error(f"消息处理失败：{ex}")

        self.logger.info("队列处理线程退出")

    def handle_pipe(self, stop_event: Event):
        self.logger.info("通讯处理线程启动")
        while not stop_event.is_set():
            try:
                if self.pipe_conn.poll(1):
                    command = self.pipe_conn.recv()
                    self.logger.info(f"管道连接接收: {command}")

                    # TODO: 通讯处理
            except EOFError:
                self.logger.warning("管道连接已关闭")
                stop_event.set()
                break
            except Exception as ex:
                self.logger.error(f"消息处理失败：{ex}")

        self.logger.info("通讯处理线程退出")

    @timer
    def _strip_track_online(self, record):
        """
        钢卷在线跟踪
        """
        timestamp = record.get("TIME_STAMP")
        selected = record.get("SELECTED")
        strip1 = Strip(
            name=record.get("STRIP_NAME_1"),
            type=record.get("STRIP_TYPE_1"),
            width=record.get("STRIP_WIDTH_1"),
            thick=record.get("STRIP_THICK_1"),
            length=record.get("STRIP_LENGTH_1"),
            weight=record.get("STRIP_WEIGHT_1"),
        )
        strip2 = Strip(
            name=record.get("STRIP_NAME_2"),
            type=record.get("STRIP_TYPE_2"),
            width=record.get("STRIP_WIDTH_2"),
            thick=record.get("STRIP_THICK_2"),
            length=record.get("STRIP_LENGTH_2"),
            weight=record.get("STRIP_WEIGHT_2"),
        )
        track_msg = TrackingMsg(
            weld1=record.get("WELD_POSITION_1"),
            weld2=record.get("WELD_POSITION_2"),
            speed=record.get("SPEED"),
        )
        self.strip_tracker.update(timestamp, selected, strip1, strip2, track_msg)

        heating_strip: Optional[Strip] = self.strip_tracker.get_heating_strip()

        if heating_strip is None:
            return None

        record["STRIP_NAME"] = heating_strip.name
        record["STRIP_TYPE"] = heating_strip.type
        record["STRIP_WIDTH"] = heating_strip.width
        record["STRIP_THICK"] = heating_strip.thick
        record["STRIP_LENGTH"] = heating_strip.length
        record["STRIP_WEIGHT"] = heating_strip.weight
        record["WELD"] = heating_strip.weld2

        return record


class RealTimeDataFrame:
    def __init__(self, configs: TrainerConfig):
        self.trainer_config = configs
        self.index = configs.index_column
        self.columns = [configs.index_column, *configs.input_columns]
        self.time_window = pd.to_timedelta(configs.freq) * configs.input_len

        # 初始化一个空的DataFrame，并将索引设置为datetime类型
        # 这是最高效写法的关键：使用时间序列作为索引
        self.df = pd.DataFrame(columns=self.columns)
        self.df.index.name = self.index
        self.df = self.df.astype({col: float for col in self.columns})  # 预设数据类型以提高效率

    def add_data(self, record: dict[str, Any]):
        """
        向DataFrame中添加新的数据行。

        Args:
            record (dict): 包含新数据的字典，必须有一个'TIMESTAMP'键，
                              其值为datetime对象。
        """
        # 1. 数据准备
        # ----------------------------------------------------
        timestamp = record.pop(self.index, None)
        if timestamp is None:
            print("警告：传入的数据字典缺少 'TIMESTAMP' 键，该数据将被忽略。")
            return

        if not isinstance(timestamp, datetime):
            raise TypeError("TIMESTAMP 的值必须是 datetime 对象。")

        # --- 这是核心修正部分 ---
        # 1. 按照 self.df.columns 的顺序，从 data_dict 中构建一个值的列表。
        #    使用 .get() 方法来安全地获取值，如果字典中缺少某个键，则使用 np.nan 作为默认值。
        #    这确保了赋值的列表与DataFrame的列完全对应。
        row_values = [record.get(col, np.nan) for col in self.columns]

        # 2. 使用这个顺序正确的列表进行赋值。
        #    这是向 .loc 赋值最明确、最不容易出错的方式。
        self.df.loc[timestamp] = row_values
        # --- 修正结束 ---

        # 确保索引是排序的，这对时间序列操作性能至关重要
        # 如果进入的时间戳保证是单调递增的，可以省略此步以获得更高性能
        # self.df.sort_index(inplace=True)
