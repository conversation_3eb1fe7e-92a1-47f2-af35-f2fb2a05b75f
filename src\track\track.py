from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class Strip:
    name: str = ""
    type: str = ""
    width: float = -1
    thick: float = -1
    length: float = -1
    weight: float = -1
    weld1: float = 0  # 焊缝位置1：距离焊机距离
    weld2: float = 0  # 焊缝位置2: 距离入炉密封辊距离
    speed: float = -1  # 速度 m/min
    loading_start_time: Optional[datetime] = None  # 上料开始时间
    loading_end_time: Optional[datetime] = None  # 上料结束时间
    head_weld_in_time: Optional[datetime] = None  # 头部焊缝入炉时间
    head_weld_out_time: Optional[datetime] = None  # 头部焊缝出炉时间
    tail_weld_in_time: Optional[datetime] = None  # 尾部焊缝入炉时间
    tail_weld_out_time: Optional[datetime] = None  # 尾部焊缝出炉时间

    def check_strip_info(self) -> bool:
        return self.type != "" and self.width > 0 and self.thick > 0 and self.length > 0 and self.weight > 0

    def update_strip_info(self, type: str, width: float, thick: float, length: float, weight: float):
        if self.type == "" and type != "":
            self.type = type

        if self.width <= 0 and width > 0:
            self.width = width

        if self.thick <= 0 and thick > 0:
            self.thick = thick

        if self.length <= 0 and length > 0:
            self.length = length

        if self.weight <= 0 and weight > 0:
            self.weight = weight


@dataclass
class TrackingMsg:
    weld1: float = 0
    weld2: float = 0
    speed: float = 0
