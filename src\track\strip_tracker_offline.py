import logging
import os
from dataclasses import asdict
from datetime import datetime
from pathlib import Path
from typing import Optional

import pandas as pd

from config import Configs
from consts import FileNameEnum as FNE

from .track import Strip, TrackingMsg


class StripTracker:
    def __init__(self, work_dir: str):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.work_dir = work_dir
        self.config = Configs.get_af_config()

        # 跟踪字典
        self.strips: dict[str, Strip] = {}
        self.out_strips: list[Strip] = []

        self.loading: str = ""
        self.heating: str = ""
        self.last_loading: str = ""
        self.last_heating: str = ""

        self.last_timestamp = None
        self.last_selected = False  # 上次上料钢卷 False-1 True-2
        self.last_speed = -1
        self.last_weld1 = -1
        self.last_weld2 = -1

    def update(
        self,
        timestamp: datetime,
        selected: bool,
        strip1: Strip,
        strip2: Strip,
        track_msg: TrackingMsg,
    ):
        try:
            self.logger.debug(
                f"[跟踪更新]-timestamp: {timestamp}, selected: {selected}, speed: {track_msg.speed}, weld1: {track_msg.weld1}, weld2: {track_msg.weld2}, strip1: {strip1.name}, strip2: {strip2.name}"
            )

            # 初始化
            if self.last_timestamp is None:
                self.loading = strip1.name if not selected else strip2.name  # 更新loading钢卷号
                self.logger.info(f"[初始化]-时间戳: {timestamp}, 上料钢卷: {self.loading}")

                self.last_timestamp = timestamp
                self.last_selected = selected
                self.last_speed = track_msg.speed
                self.last_weld1 = track_msg.weld1
                self.last_weld2 = track_msg.weld2

                return

            if self.last_timestamp >= timestamp:
                self.logger.info(f"时间戳异常，此条数据将被丢弃, last_timestamp: {self.last_timestamp}, current timestamp: {timestamp}")
                return

            # 检查并记录钢卷
            self._add_strips(strip1)
            self._add_strips(strip2)

            # 钢卷实例
            loading_strip = self._get_strip(self.loading)
            last_loading_strip = self._get_strip(self.last_loading)
            heating_strip = self._get_strip(self.heating)
            last_heating_strip = self._get_strip(self.last_heating)

            loading_update = False
            ready_loading = strip1.name if not selected else strip2.name
            if self.last_selected != selected or ready_loading != self.loading:  # selected 变更或上料批号更改 触发上料
                self.last_loading = self.loading  # 更新上次上料钢卷号
                self.loading = ready_loading  # 更新上料钢卷号

                loading_strip = self._get_strip(self.loading)  # 新上料钢卷实例
                last_loading_strip = self._get_strip(self.last_loading)  # 上次上料钢卷实例

                if loading_strip is not None and self.loading != self.last_loading:
                    loading_strip.loading_start_time = timestamp  # 更新上料开始时间

                if last_loading_strip is not None and self.loading != self.last_loading:
                    last_loading_strip.loading_end_time = timestamp  # 更新上料结束时间

                self.logger.info(f"[1-上料]-上料时间: {timestamp}, 上料钢卷: {self.loading}, 上料结束钢卷: {self.last_loading}")
                loading_update = True

            heating_update = False
            # weld2 改变触发入炉
            if (self.last_weld2 >= self.config.weld2_max - 30 or self.last_weld2 == 0) and (track_msg.weld2 > 0 and track_msg.weld2 < 50):
                self.last_heating = self.heating  # 更新入炉钢卷号
                self.heating = self.loading

                last_heating_strip = self._get_strip(self.last_heating)
                heating_strip = self._get_strip(self.heating)

                if last_heating_strip is not None and self.heating != self.last_heating:
                    last_heating_strip.tail_weld_in_time = timestamp  # 更新出炉时间

                if heating_strip is not None and self.heating != self.last_heating:
                    heating_strip.head_weld_in_time = timestamp  # 更新入炉时间

                self.logger.info(f"[2-入炉]-入炉时间: {timestamp}, 入炉钢卷: {self.heating}, 入炉结束钢卷: {self.last_heating}")
                heating_update = True

            # 更新速度
            if loading_strip is not None:
                loading_strip.speed = track_msg.speed
            if last_loading_strip is not None:
                last_loading_strip.speed = track_msg.speed
            if heating_strip is not None:
                heating_strip.speed = track_msg.speed
            if last_heating_strip is not None:
                last_heating_strip.speed = track_msg.speed

            # 更新焊缝位置
            delta_t = (timestamp - self.last_timestamp).total_seconds()
            speed_s = track_msg.speed / 60
            distance = speed_s * delta_t
            # self.logger.debug(
            #     f"[位置更新]-时间间隔: {delta_t}s, 速度: {track_msg.speed} m/min {speed_s} m/s, \
            # 前进距离: {distance}m, 焊缝位置1: {track_msg.weld1}m, 焊缝位置2: {track_msg.weld2}m"
            # )

            if loading_update:
                # 上料更新，新上料钢卷位置初始化为0
                if loading_strip is not None:
                    loading_strip.weld1 = 0
                # 更新上次上料钢卷焊缝位置
                if last_loading_strip is not None:
                    last_loading_strip.weld1 += distance
            else:
                if loading_strip is not None:
                    if track_msg.weld1 > self.last_weld1:
                        loading_strip.weld1 = track_msg.weld1
                    else:
                        loading_strip.weld1 += distance
                if last_loading_strip is not None:
                    last_loading_strip.weld1 += distance

            if heating_update:
                # 入炉更新，新入炉钢卷位置初始化为0
                if heating_strip is not None:
                    heating_strip.weld2 = track_msg.weld2
                # 更新上次入炉钢卷焊缝位置
                if last_heating_strip is not None:
                    last_heating_strip.weld2 += distance
            else:
                if heating_strip is not None:
                    if track_msg.weld2 > self.last_weld2:
                        heating_strip.weld2 = track_msg.weld2
                    else:
                        heating_strip.weld2 += distance
                if last_heating_strip is not None:
                    last_heating_strip.weld2 += distance

            # 触发完全出炉
            # if heating_strip is not None and last_heating_strip is not None and heating_strip.weld2 >= self.config.total_length:
            if self.last_weld2 < self.config.total_length and track_msg.weld2 >= self.config.total_length:
                self.logger.info(f"[3-出炉]-出炉时间: {timestamp}, 完全出炉钢卷: {self.last_heating}")

                if last_heating_strip is not None and self.last_heating != self.heating:
                    last_heating_strip.tail_weld_out_time = timestamp
                    self.out_strips.append(last_heating_strip)
                    self._del_strip(self.last_heating)
                    self.last_heating = ""

                if heating_strip is not None and self.heating != self.last_heating:
                    heating_strip.head_weld_out_time = timestamp

            # 更新时间戳
            self.last_timestamp = timestamp
            self.last_selected = selected
            self.last_speed = track_msg.speed
            self.last_weld1 = track_msg.weld1
            self.last_weld2 = track_msg.weld2

        except Exception as e:
            self.logger.error(f"更新钢卷跟踪失败: {e}")

    def get_heating_strip(self):
        return self._get_strip(self.heating)

    def _add_strips(self, strip: Strip) -> bool:
        if strip.name is None or strip.name == "":
            return False

        if strip.name in self.strips:  # 已存在的钢卷更新信息，不新增钢卷实例
            if not self.strips[strip.name].check_strip_info():
                self.strips[strip.name].update_strip_info(strip.type, strip.width, strip.thick, strip.length, strip.weight)
            return False

        self.strips[strip.name] = strip
        return True

    def _get_strip(self, key: str) -> Optional[Strip]:
        if key is None or key.strip() == "":
            return None

        return self.strips.get(key, None)

    def _del_strip(self, key: str) -> bool:
        if key not in self.strips:
            return False
        del self.strips[key]
        return True

    def save_tracking_data(self):
        try:
            os.makedirs(self.work_dir, exist_ok=True)  # 创建工作目录，如果不存在的话
            df = pd.DataFrame([asdict(strip) for strip in self.out_strips])
            df.to_csv(Path(self.work_dir, f"{FNE.TRACK_HIS.value}.csv"), index=False)
        except Exception as e:
            self.logger.error(f"保存跟踪数据失败: {e}")
