import logging
import time
from multiprocessing.connection import Connection

from config.configs import Configs
from config.logger import Logger
from consts import LogFileNameEnum as LFN


class MpcService:
    """
    系统状态管理服务
    """

    def __init__(self, pipe_conn: Connection):
        Configs.initialize()
        Logger.initialize(file_name=LFN.MPC_STATE.value)

        self.logger = logging.getLogger(self.__class__.__name__)
        self.pipe_conn = pipe_conn

    def start(self):
        self.logger.info("MPC 服务已启动.")

        while True:
            try:
                self.pipe_conn.send("MPC Service TEST")

                time.sleep(30)
            except KeyboardInterrupt:
                self.logger.warning("收到中断信号，正在停止服务...")
                break
            except Exception as ex:
                self.logger.error(f"消息处理失败：{ex}")
