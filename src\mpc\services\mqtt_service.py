import json
import logging
from datetime import datetime
from multiprocessing import Queue
from typing import Any

from config.configs import Configs
from config.logger import Logger
from consts import LogFileNameEnum as LFN
from mpc.listeners.mqtt_listener import MQTTListener


class MQTTService:
    def __init__(self, output_queues: list[Queue]):
        Configs.initialize()
        Logger.initialize(file_name=LFN.MPC_MQTT.value)

        self.INDEX_COLUMN = Configs.get_train_config().index_column

        self.logger = logging.getLogger(self.__class__.__name__)
        self.listener = MQTTListener(Configs.get_mqtt_config(), self.on_message_callback)
        self.output_queues = output_queues

    def on_message_callback(self, topic: str, payload: Any):
        self.logger.info(f"收到消息，主题: {topic}, 内容: {payload}")

        try:
            payload = self._process_message(payload)
            self._send_message(payload)
        except Exception as ex:
            self.logger.error(f"消息处理失败：{ex}")

    def start(self):
        self.listener.start()

    def stop(self):
        self.listener.stop()

    def _process_message(self, message: Any):
        if message is None:
            raise ValueError("消息为空")

        # 安全解析消息
        if isinstance(message, str):
            try:
                message = json.loads(message)
            except json.JSONDecodeError:
                raise ValueError("消息格式错误，无法解析JSON")

        if not isinstance(message, dict):
            raise ValueError("消息格式错误，应为字典")

        data = {}
        # 单次获取并检查metrics列表
        metrics = message.get("metrics")
        if not isinstance(metrics, list):
            return data

        index_col = self.INDEX_COLUMN  # 缓存索引列名
        index_set = False  # 跟踪索引列是否已设置

        for metric in metrics:
            # 处理时间戳（仅设置一次）
            if not index_set:
                timestamp = metric.get("timestamp")
                if timestamp is not None:
                    data[index_col] = datetime.fromtimestamp(timestamp)
                    index_set = True

            # 批量处理字段
            fields = metric.get("fields")
            if isinstance(fields, dict):
                data.update(fields)

        return data

    def _send_message(self, payload: Any):
        for queue in self.output_queues:
            try:
                queue.put_nowait(payload)
            except Exception as ex:
                self.logger.error(f"发送消息失败：{ex}")
