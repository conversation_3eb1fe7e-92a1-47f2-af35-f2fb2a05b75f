import logging
from datetime import datetime, timedelta
import pandas as pd

from typing import Any, Generator, Optional
from influxdb import InfluxD<PERSON>lient
from influxdb.client import ResultSet


class InfluxQueryWrapper:
    __QUERY = "SELECT {} FROM {}"
    __ONLY_FIELD = '"{}"'
    __MEAN = 'MEAN("{}") AS "{}"'
    __COUNT = 'COUNT("{}")'
    __MAX = 'MAX("{}") AS "{}"'
    __MIN = 'MIN("{}") AS "{}"'
    __SUM = 'SUM("{}")'
    __MEDIAN = 'MEDIAN("{}")'
    __PERCENTILE = 'PERCENTILE("{}", {})'

    # Data type casting
    __DATA_TYPE = "::{}"

    # Where clause operators
    __WHERE = " WHERE"
    __AND = " AND"
    __OR = " OR"

    # Comparison operators
    __OPERATOR_EQ = " {} = {}"
    __OPERATOR_NEQ = " {} != {}"
    __OPERATOR_GT = " {} > {}"
    __OPERATOR_GE = " {} >= {}"
    __OPERATOR_LT = " {} < {}"
    __OPERATOR_LE = " {} <= {}"
    __OPERATOR_IN = " {} IN ({})"
    __OPERATOR_LIKE = " {} LIKE {}"

    # Sorting
    __ORDER_BY = " ORDER BY {}"
    __ASC = " ASC"
    __DESC = " DESC"

    # Group by
    __GROUP_BY = " GROUP BY {}"
    __GROUP_BY_TIME = " GROUP BY time({})"
    __GROUP_BY_TIME_OFFSET = " GROUP BY time({},{})"
    __FILL = " fill({})"

    # Limit
    __LIMIT = " LIMIT {}"

    # Time zone
    __TIME_ZONE = " tz('{}')"

    def __init__(
        self,
        *measurements,
        field: str | list[str] = "value",
        aggregation=None,
        tz="Asia/Shanghai",
    ):
        """
        InfluxDB 查询包装类

        :param measurements: 测量名称，可以是元组、列表或多个参数
        :param field: 查询字段名称，可以是字符串（单字段）或列表/元组（多字段）
        :param aggregation: 聚合方式，例如 "mean", "sum", "count" 等
        :param tz: 时区，默认为 "Asia/Shanghai"
        """

        self.__measurements = measurements
        # 支持多字段查询：将field统一转换为列表格式
        if isinstance(field, (list, tuple)):
            self.__fields = list(field)
        else:
            self.__fields = [field]
        self.__aggregation = aggregation
        self.__conditions = {
            "QUERY": None,
            "OPERATOR": [],
            "ORDER_BY": None,
            "GROUP_BY": None,
            "LIMIT": None,
            "TIME_ZONE": None,
        }
        self.__query_prefix(self.__measurements, self.__fields, aggregation)
        # self.time_zone(tz)

    def __query_prefix(self, measurements, fields, aggregation):
        """
        构建查询的基础部分，设置 SELECT 子句
        支持多字段查询
        """
        if aggregation is None:
            # 多字段查询：为每个字段生成查询部分
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__ONLY_FIELD.format(field))
            prefix = ", ".join(field_parts)
        elif aggregation == "count":
            # 对多字段应用聚合函数
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__COUNT.format(field))
            prefix = ", ".join(field_parts)
        elif aggregation == "mean":
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__MEAN.format(field, field))
            prefix = ", ".join(field_parts)
        elif aggregation == "max":
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__MAX.format(field, field))
            prefix = ", ".join(field_parts)
        elif aggregation == "min":
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__MIN.format(field, field))
            prefix = ", ".join(field_parts)
        elif aggregation == "sum":
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__SUM.format(field))
            prefix = ", ".join(field_parts)
        elif aggregation == "median":
            field_parts = []
            for field in fields:
                field_parts.append(InfluxQueryWrapper.__MEDIAN.format(field))
            prefix = ", ".join(field_parts)
        else:
            prefix = "*"

        measurements_str = ", ".join([f'"{m}"' for m in measurements])
        query = InfluxQueryWrapper.__QUERY.format(prefix, measurements_str)
        self.__conditions["QUERY"] = query

    def __check_operator(self):
        """确保操作符列表已初始化"""
        if not self.__conditions["OPERATOR"]:
            self.__conditions["OPERATOR"] = []

    def __operator_handler(self, condition, column, value):
        """处理条件操作符"""
        # 如果是时间字段，确保使用正确的时间格式
        if column == "time" and isinstance(value, str):
            try:
                # 判断是否为"YYYY-MM-DD HH:MM:SS"格式
                if " " in value:
                    dt = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                    # 转换为InfluxDB接受的RFC3339/ISO8601格式
                    value = dt.strftime("%Y-%m-%dT%H:%M:%SZ")
            except ValueError:
                # 如果无法解析，保留原值，可能已经是有效的格式
                pass

        if isinstance(value, str):
            value = f"'{value}'"  # 字符串类型需要加单引号，但time类型不需要
        condition = condition.format(column, value)
        self.__conditions["OPERATOR"].append(condition)

    def eq(self, column, value) -> "InfluxQueryWrapper":
        """column == value"""
        condition = InfluxQueryWrapper.__OPERATOR_EQ
        self.__check_operator()
        self.__operator_handler(condition, column, value)
        return self

    def neq(self, column, value) -> "InfluxQueryWrapper":
        """column != value"""
        condition = InfluxQueryWrapper.__OPERATOR_NEQ
        self.__check_operator()
        self.__operator_handler(condition, column, value)
        return self

    def gt(self, column, value) -> "InfluxQueryWrapper":
        """column > value"""
        condition = InfluxQueryWrapper.__OPERATOR_GT
        self.__check_operator()
        self.__operator_handler(condition, column, value)
        return self

    def ge(self, column, value) -> "InfluxQueryWrapper":
        """column >= value"""
        condition = InfluxQueryWrapper.__OPERATOR_GE
        self.__check_operator()
        self.__operator_handler(condition, column, value)
        return self

    def lt(self, column, value) -> "InfluxQueryWrapper":
        """column < value"""
        condition = InfluxQueryWrapper.__OPERATOR_LT
        self.__check_operator()
        self.__operator_handler(condition, column, value)
        return self

    def le(self, column, value) -> "InfluxQueryWrapper":
        """column <= value"""
        condition = InfluxQueryWrapper.__OPERATOR_LE
        self.__check_operator()
        self.__operator_handler(condition, column, value)
        return self

    def in_(self, column, values) -> "InfluxQueryWrapper":
        """column IN (value1, value2, ...)"""
        condition = InfluxQueryWrapper.__OPERATOR_IN
        self.__check_operator()
        # 正确处理字符串值，为字符串类型的值添加单引号
        formatted_values = []
        for v in values:
            if isinstance(v, str):
                formatted_values.append(f"'{v}'")
            else:
                formatted_values.append(str(v))
        values_str = ", ".join(formatted_values)
        # 直接使用格式化后的值，不再通过__operator_handler处理
        condition_str = condition.format(column, values_str)
        self.__conditions["OPERATOR"].append(condition_str)
        return self

    def like(self, column, pattern) -> "InfluxQueryWrapper":
        """column LIKE 'pattern'"""
        condition = InfluxQueryWrapper.__OPERATOR_LIKE
        self.__check_operator()
        self.__operator_handler(condition, column, f"'{pattern}'")
        return self

    def or_(self, column, value) -> "InfluxQueryWrapper":
        """column OR value"""
        condition = InfluxQueryWrapper.__OPERATOR_EQ
        self.__check_operator()
        self.__operator_handler(InfluxQueryWrapper.__OPERATOR_EQ, column, value)
        return self

    def order_by(self, column) -> "InfluxQueryWrapper":
        """按列排序"""
        condition = InfluxQueryWrapper.__ORDER_BY.format(column)
        self.__conditions["ORDER_BY"] = condition
        return self

    def order_by_asc(self, column) -> "InfluxQueryWrapper":
        """按列升序排序"""
        condition = (
            InfluxQueryWrapper.__ORDER_BY.format(column) + InfluxQueryWrapper.__ASC
        )
        self.__conditions["ORDER_BY"] = condition
        return self

    def order_by_desc(self, column) -> "InfluxQueryWrapper":
        """按列降序排序"""
        condition = (
            InfluxQueryWrapper.__ORDER_BY.format(column) + InfluxQueryWrapper.__DESC
        )
        self.__conditions["ORDER_BY"] = condition
        return self

    def group_by(self, column) -> "InfluxQueryWrapper":
        """按列分组"""
        condition = InfluxQueryWrapper.__GROUP_BY.format(column)
        self.__conditions["GROUP_BY"] = condition
        return self

    def group_by_time(self, interval, offset=None, fill=None) -> "InfluxQueryWrapper":
        """按时间分组"""
        if offset is None:
            condition = InfluxQueryWrapper.__GROUP_BY_TIME.format(interval)
        else:
            condition = InfluxQueryWrapper.__GROUP_BY_TIME_OFFSET.format(
                interval, offset
            )

        if fill is not None:
            condition += InfluxQueryWrapper.__FILL.format(fill)

        self.__conditions["GROUP_BY"] = condition
        return self

    def limit(self, value) -> "InfluxQueryWrapper":
        """限制返回的结果数量"""
        condition = InfluxQueryWrapper.__LIMIT.format(value)
        self.__conditions["LIMIT"] = condition
        return self

    def time_zone(self, tz) -> "InfluxQueryWrapper":
        """设置时区"""
        condition = InfluxQueryWrapper.__TIME_ZONE.format(tz)
        self.__conditions["TIME_ZONE"] = condition
        return self

    def build(self) -> str:
        """构建最终查询语句"""
        influxQL = self.__conditions["QUERY"]

        # 添加 WHERE 子句
        if self.__conditions["OPERATOR"]:
            influxQL += InfluxQueryWrapper.__WHERE
            # 使用 AND 连接多个条件
            influxQL += self.__AND.join(self.__conditions["OPERATOR"])

        # 添加 ORDER BY 子句
        if self.__conditions["ORDER_BY"]:
            influxQL += self.__conditions["ORDER_BY"]

        # 添加 GROUP BY 子句
        if self.__conditions["GROUP_BY"]:
            influxQL += self.__conditions["GROUP_BY"]

        # 添加 LIMIT 子句
        if self.__conditions["LIMIT"]:
            influxQL += self.__conditions["LIMIT"]

        # 添加时区
        if self.__conditions["TIME_ZONE"]:
            influxQL += self.__conditions["TIME_ZONE"]

        return influxQL


class InfluxQuery:
    def __init__(self, host, port, username, password, database):
        """初始化 InfluxDB 客户端"""
        self.conn = InfluxDBClient(
            host, port, username, password, database, timeout=30 * 60 * 1000
        )
        self.logger = logging.getLogger(__class__.__name__)

    def query(
        self, influx_ql
    ) -> Generator[ResultSet, Any, None] | ResultSet | list[ResultSet]:
        """执行查询"""
        try:
            result = self.conn.query(influx_ql)
            return result
        except Exception as e:
            self.logger.error("InfluxDB 查询失败: " + str(e))
            raise e

    def query_df(self, influx_ql) -> pd.DataFrame | dict | list:
        """执行查询并返回DataFrame或DataFrame字典

        返回类型:
            - 当查询结果包含单个表时，返回DataFrame
            - 当查询结果包含多个表时，返回字典，键为(测量名称, 标签元组)，值为DataFrame
            - 当查询结果为列表时，返回DataFrame列表
        """
        try:
            result = self.conn.query(influx_ql)
            return self._to_dataframe(result)
        except Exception as e:
            self.logger.error("InfluxDB 查询失败: " + str(e))
            raise e

    def query_by_wrapper(
        self, query_wrapper
    ) -> Generator[ResultSet, Any, None] | ResultSet | list[ResultSet]:
        """使用 QueryWrapper 执行查询"""
        influxQL = query_wrapper.build()
        self.logger.debug(influxQL)
        return self.query(influxQL)

    def query_df_by_wrapper(self, query_wrapper) -> pd.DataFrame | dict | list:
        """使用 QueryWrapper执行查询，并返回DataFrame或DataFrame字典

        返回类型:
            - 当查询结果包含单个表时，返回DataFrame
            - 当查询结果包含多个表时，返回字典，键为(测量名称, 标签元组)，值为DataFrame
            - 当查询结果为列表时，返回DataFrame列表
        """
        influxQL = query_wrapper.build()
        self.logger.debug(influxQL)
        return self.query_df(influxQL)

    def batch_query_aligned_df(
        self,
        measurements,
        field: str | list[str] = "value",  # 支持单字段或多字段查询
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        time_interval: Optional[str] = None,
        fill_method: Optional[str] = None,
        resample=False,
        chunk_size: str = "1h",  # 新增：分块大小，默认1小时
    ) -> pd.DataFrame | pd.Series | None:
        """批量查询多个measurements并按时间戳对齐

        将多个测量点的数据查询出来，并按时间戳对齐合并成一个DataFrame。
        每个测量点的值会成为DataFrame中的一列，列名为测量点名称。

        采用单个查询模式：对每个measurement分别进行独立查询，避免InfluxDB v1
        联合查询时的数据类型冲突问题，确保所有数据类型的measurement都能被正确处理。

        支持智能分块查询机制，自动将大时间范围的查询分割成多个小块，
        避免内存溢出和查询超时问题。

        参数:
            measurements: 要查询的测量点列表，最多支持20个
            field: 查询的字段名称，可以是字符串（单字段）或列表/元组（多字段），默认为"value"
            start_time: 开始时间，datetime对象
            end_time: 结束时间，datetime对象
            time_interval: 重采样时间间隔，默认为None
            fill_method: 缺失值填充方法，可选["linear", "ffill", "bfill", "nearest"]，默认为None
            resample: 是否需要重采样数据，默认为False
            chunk_size: 分块大小，支持pandas时间频率格式，如"1h"(1小时)、"30min"(30分钟)、"1d"(1天)等，默认为"1h"

        返回:
            包含所有测量点数据的DataFrame，索引为时间戳
            - 单字段查询：列名为测量点名称
            - 多字段查询：列名为"测量点名称_字段名称"的格式
        """
        if len(measurements) > 20:
            self.logger.warning(
                f"查询的测量点过多({len(measurements)}个)，可能导致性能问题。建议每次查询不超过20个测量点。"
            )

        # 检查时间范围参数
        if start_time is None or end_time is None:
            self.logger.warning(
                "未指定时间范围，将直接查询所有数据（可能导致性能问题）"
            )
            return self._query_without_chunking(
                measurements,
                field,
                start_time,
                end_time,
                time_interval,
                fill_method,
                resample,
            )

        # 计算时间跨度和是否需要分块
        time_span = end_time - start_time
        chunk_timedelta = pd.Timedelta(chunk_size)

        # 如果时间跨度小于等于分块大小，直接查询
        if time_span <= chunk_timedelta:
            self.logger.info(
                f"时间跨度({time_span})小于分块大小({chunk_size})，使用直接查询"
            )
            return self._query_without_chunking(
                measurements,
                field,
                start_time,
                end_time,
                time_interval,
                fill_method,
                resample,
            )

        # 使用分块查询
        self.logger.info(
            f"时间跨度({time_span})较大，使用分块查询，分块大小: {chunk_size}"
        )
        return self._query_with_chunking(
            measurements,
            field,
            start_time,
            end_time,
            time_interval,
            fill_method,
            resample,
            chunk_size,
        )

    def _query_without_chunking(
        self,
        measurements,
        field: str | list[str],
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        time_interval: Optional[str],
        fill_method: Optional[str],
        resample: bool,
    ) -> pd.DataFrame | pd.Series | None:
        """不使用分块的单个查询方法"""
        self.logger.info(f"开始单个查询模式，查询 {len(measurements)} 个测量点")

        # 对每个measurement分别进行单独查询
        measurement_results = {}
        successful_queries = 0

        for measurement in measurements:
            try:
                self.logger.debug(f"正在查询测量点: {measurement}")

                # 为每个measurement创建独立的查询对象
                qw = InfluxQueryWrapper(measurement, field=field)

                # 添加时间范围条件
                if start_time:
                    qw.gt("time", start_time.strftime("%Y-%m-%dT%H:%M:%SZ"))
                if end_time:
                    qw.le("time", end_time.strftime("%Y-%m-%dT%H:%M:%SZ"))

                # 执行单个measurement查询
                result = self.query_df_by_wrapper(qw)

                if result is not None:
                    measurement_results[measurement] = result
                    successful_queries += 1
                    self.logger.debug(f"测量点 {measurement} 查询成功")
                else:
                    self.logger.warning(f"测量点 {measurement} 查询结果为空")

            except Exception as e:
                self.logger.error(f"测量点 {measurement} 查询失败: {str(e)}")
                continue

        self.logger.info(
            f"单个查询完成，成功查询 {successful_queries}/{len(measurements)} 个测量点"
        )

        # 合并所有单个查询的结果
        return self._combine_measurement_results(
            measurement_results, field, time_interval, fill_method, resample
        )

    def _query_with_chunking(
        self,
        measurements,
        field: str | list[str],
        start_time: datetime,
        end_time: datetime,
        time_interval: Optional[str],
        fill_method: Optional[str],
        resample: bool,
        chunk_size: str,
    ) -> pd.DataFrame | pd.Series | None:
        """使用分块查询的方法"""
        chunk_timedelta = pd.Timedelta(chunk_size)
        current_time = start_time
        all_chunks = []
        chunk_count = 0

        # 计算总的分块数量用于进度显示
        total_chunks = int((end_time - start_time) / chunk_timedelta) + 1
        self.logger.info(f"开始分块查询，预计分为 {total_chunks} 个块")

        while current_time < end_time:
            chunk_count += 1
            chunk_end_time = min(current_time + chunk_timedelta, end_time)

            self.logger.info(
                f"正在查询第 {chunk_count}/{total_chunks} 块: {current_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {chunk_end_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

            try:
                # 查询当前时间块
                chunk_result = self._query_single_chunk(
                    measurements, field, current_time, chunk_end_time
                )

                if chunk_result is not None and not chunk_result.empty:
                    all_chunks.append(chunk_result)
                    self.logger.info(
                        f"第 {chunk_count} 块查询成功，获得 {len(chunk_result)} 行数据"
                    )
                else:
                    self.logger.info(f"第 {chunk_count} 块无数据")

            except Exception as e:
                self.logger.error(f"第 {chunk_count} 块查询失败: {str(e)}")
                # 继续查询下一块，不中断整个过程

            current_time = chunk_end_time

        # 合并所有分块结果
        if not all_chunks:
            self.logger.warning("所有分块查询都没有返回数据")
            return pd.DataFrame()

        self.logger.info(f"开始合并 {len(all_chunks)} 个分块的数据")
        combined_df = pd.concat(all_chunks, axis=0).sort_index()

        # 去除重复的时间戳（可能在分块边界处出现）
        combined_df = combined_df[~combined_df.index.duplicated(keep="first")]

        # 应用重采样和填充
        if (
            resample
            and time_interval is not None
            and fill_method is not None
            and not combined_df.empty
        ):
            combined_df = self._apply_resample_and_fill(
                combined_df, time_interval, fill_method
            )

        self.logger.info(f"分块查询完成，最终获得 {len(combined_df)} 行数据")
        return combined_df

    def _query_single_chunk(
        self,
        measurements,
        field: str | list[str],
        start_time: datetime,
        end_time: datetime,
    ) -> pd.DataFrame | pd.Series | None:
        """查询单个时间块的数据，使用单个查询模式"""
        # 对每个measurement分别进行单独查询
        measurement_results = {}

        for measurement in measurements:
            try:
                # 为每个measurement创建独立的查询对象
                qw = InfluxQueryWrapper(measurement, field=field)
                qw.gt("time", start_time.strftime("%Y-%m-%dT%H:%M:%SZ"))
                qw.le("time", end_time.strftime("%Y-%m-%dT%H:%M:%SZ"))

                # 执行单个measurement查询
                result = self.query_df_by_wrapper(qw)

                if result is not None:
                    measurement_results[measurement] = result

            except Exception as e:
                self.logger.warning(f"分块查询中测量点 {measurement} 失败: {str(e)}")
                continue

        # 合并所有单个查询的结果，但不进行重采样和填充（在最后统一处理）
        return self._combine_measurement_results(
            measurement_results, field, None, None, False
        )

    def _combine_measurement_results(
        self,
        measurement_results: dict,
        field: str | list[str],
        time_interval: Optional[str],
        fill_method: Optional[str],
        resample: bool,
    ) -> pd.DataFrame | pd.Series | None:
        """合并多个单独查询的measurement结果

        Args:
            measurement_results: 字典，键为measurement名称，值为查询结果DataFrame
            field: 查询的字段名称
            time_interval: 重采样时间间隔
            fill_method: 填充方法
            resample: 是否重采样

        Returns:
            合并后的DataFrame
        """
        if not measurement_results:
            self.logger.warning("没有可合并的measurement结果")
            return pd.DataFrame()

        self.logger.info(f"开始合并 {len(measurement_results)} 个measurement的查询结果")

        # 创建一个空的DataFrame用于存储合并结果
        aligned_df = pd.DataFrame()
        field_list = field if isinstance(field, (list, tuple)) else [field]

        # 统计数据类型信息
        data_type_stats = {
            "numeric": 0,
            "string": 0,
            "boolean": 0,
            "datetime": 0,
            "other": 0,
        }
        processed_measurements = 0

        for measurement_name, result in measurement_results.items():
            try:
                # 处理单个measurement的结果
                if isinstance(result, pd.DataFrame):
                    df = result
                elif isinstance(result, dict) and len(result) == 1:
                    # 如果是字典格式，提取DataFrame
                    df = list(result.values())[0]
                else:
                    self.logger.warning(
                        f"测量点 {measurement_name} 的结果格式不支持: {type(result)}"
                    )
                    continue

                if df.empty:
                    self.logger.debug(f"测量点 {measurement_name} 的结果为空")
                    continue

                # 处理列重命名
                rename_dict = {}
                columns_to_merge = []

                # 首先尝试找到指定的字段
                for field_name in field_list:
                    if field_name in df.columns:
                        if len(field_list) > 1:
                            new_col_name = f"{measurement_name}_{field_name}"
                        else:
                            new_col_name = measurement_name
                        rename_dict[field_name] = new_col_name
                        columns_to_merge.append(new_col_name)

                # 如果没有找到指定字段，使用所有可用列
                if not rename_dict:
                    self.logger.debug(
                        f"测量点 {measurement_name} 未找到指定字段 {field_list}，使用所有可用列"
                    )
                    for col in df.columns:
                        if len(field_list) > 1 or len(df.columns) > 1:
                            new_col_name = f"{measurement_name}_{col}"
                        else:
                            new_col_name = measurement_name
                        rename_dict[col] = new_col_name
                        columns_to_merge.append(new_col_name)

                        # 统计数据类型
                        dtype_str = str(df[col].dtype)
                        if dtype_str in [
                            "int64",
                            "float64",
                            "int32",
                            "float32",
                            "int16",
                            "float16",
                        ]:
                            data_type_stats["numeric"] += 1
                        elif dtype_str in ["bool"]:
                            data_type_stats["boolean"] += 1
                        elif dtype_str in ["object", "string"]:
                            data_type_stats["string"] += 1
                        elif "datetime" in dtype_str:
                            data_type_stats["datetime"] += 1
                        else:
                            data_type_stats["other"] += 1

                # 应用重命名
                if rename_dict:
                    df = df.rename(columns=rename_dict)
                    processed_measurements += 1

                # 合并到结果DataFrame
                if aligned_df.empty:
                    aligned_df = df[columns_to_merge] if columns_to_merge else df
                else:
                    merge_cols = (
                        columns_to_merge if columns_to_merge else df.columns.tolist()
                    )
                    try:
                        aligned_df = pd.merge(
                            aligned_df,
                            df[merge_cols],
                            left_index=True,
                            right_index=True,
                            how="outer",
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"合并测量点 {measurement_name} 数据时出现警告: {e}"
                        )
                        # 尝试使用 concat 作为备选方案
                        try:
                            aligned_df = pd.concat(
                                [aligned_df, df[merge_cols]], axis=1, sort=False
                            )
                        except Exception as e2:
                            self.logger.error(
                                f"使用 concat 合并测量点 {measurement_name} 数据也失败: {e2}"
                            )
                            continue

                self.logger.debug(
                    f"成功合并测量点 {measurement_name}，当前结果形状: {aligned_df.shape}"
                )

            except Exception as e:
                self.logger.error(f"处理测量点 {measurement_name} 时发生错误: {str(e)}")
                continue

        # 记录处理统计信息
        if processed_measurements > 0:
            self.logger.info(
                f"成功合并 {processed_measurements}/{len(measurement_results)} 个测量点"
            )
            if any(data_type_stats.values()):
                self.logger.info(
                    f"数据类型统计: 数值列={data_type_stats['numeric']}, "
                    f"字符串列={data_type_stats['string']}, 布尔列={data_type_stats['boolean']}, "
                    f"时间列={data_type_stats['datetime']}, 其他列={data_type_stats['other']}"
                )

        # 时间戳重排序
        if not aligned_df.empty:
            aligned_df = aligned_df.sort_index()

        # 是否需要重采样数据
        if resample and not aligned_df.empty and time_interval and fill_method:
            self.logger.debug("开始应用重采样和填充，支持混合数据类型")
            aligned_df = self._apply_resample_and_fill(
                aligned_df, time_interval, fill_method
            )

        return aligned_df

    def _process_query_result(
        self,
        result,
        measurements,
        field: str | list[str],
        time_interval: Optional[str],
        fill_method: Optional[str],
        resample: bool,
    ) -> pd.DataFrame | pd.Series | None:
        """处理查询结果，进行数据对齐和列重命名

        支持混合数据类型的 measurement，包括：
        - 数值类型（int, float）
        - 字符串类型（object, string）
        - 布尔类型（bool）
        - 时间类型（datetime64）
        """
        # 如果结果是单个DataFrame，转换为字典格式
        if isinstance(result, pd.DataFrame):
            # 从索引中提取measurement名称
            series_name = measurements[0] if len(measurements) == 1 else None
            result = {series_name: result}

        # 创建一个空的DataFrame用于存储合并结果
        aligned_df = pd.DataFrame()

        # 统计数据类型信息
        total_measurements = len(measurements)
        processed_measurements = 0
        data_type_stats = {
            "numeric": 0,
            "string": 0,
            "boolean": 0,
            "datetime": 0,
            "other": 0,
        }

        # 处理每个测量点的数据
        if isinstance(result, dict):
            for key, df in result.items():
                # 提取测量点名称
                if isinstance(key, tuple):
                    measurement_name = key[0]
                else:
                    measurement_name = key

                # 处理多字段查询的列重命名
                field_list = field if isinstance(field, (list, tuple)) else [field]
                rename_dict = {}
                columns_to_merge = []

                for field_name in field_list:
                    if field_name in df.columns:
                        # 多字段时使用"测量点_字段"格式，单字段时直接使用测量点名称
                        if len(field_list) > 1:
                            new_col_name = f"{measurement_name}_{field_name}"
                        else:
                            new_col_name = measurement_name
                        rename_dict[field_name] = new_col_name
                        columns_to_merge.append(new_col_name)

                # 如果没有找到指定字段，使用所有可用列（包括非数值列）
                if not rename_dict:
                    self.logger.debug(
                        f"测量点 {measurement_name} 未找到指定字段 {field_list}，使用所有可用列"
                    )
                    for col in df.columns:
                        # 包含所有数据类型：数值、字符串、布尔、时间等
                        if len(field_list) > 1 or len(df.columns) > 1:
                            new_col_name = f"{measurement_name}_{col}"
                        else:
                            new_col_name = measurement_name
                        rename_dict[col] = new_col_name
                        columns_to_merge.append(new_col_name)

                        # 统计数据类型
                        dtype_str = str(df[col].dtype)
                        if dtype_str in [
                            "int64",
                            "float64",
                            "int32",
                            "float32",
                            "int16",
                            "float16",
                        ]:
                            data_type_stats["numeric"] += 1
                        elif dtype_str in ["bool"]:
                            data_type_stats["boolean"] += 1
                        elif dtype_str in ["object", "string"]:
                            data_type_stats["string"] += 1
                        elif "datetime" in dtype_str:
                            data_type_stats["datetime"] += 1
                        else:
                            data_type_stats["other"] += 1

                        self.logger.debug(
                            f"  包含列: {col} ({df[col].dtype}) -> {new_col_name}"
                        )

                # 应用重命名
                if rename_dict:
                    df = df.rename(columns=rename_dict)
                    processed_measurements += 1

                # 合并到结果DataFrame
                if aligned_df.empty:
                    aligned_df = df[columns_to_merge] if columns_to_merge else df
                else:
                    merge_cols = (
                        columns_to_merge if columns_to_merge else df.columns.tolist()
                    )
                    try:
                        aligned_df = pd.merge(
                            aligned_df,
                            df[merge_cols],
                            left_index=True,
                            right_index=True,
                            how="outer",
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"合并测量点 {measurement_name} 数据时出现警告: {e}"
                        )
                        # 尝试使用 concat 作为备选方案
                        try:
                            aligned_df = pd.concat(
                                [aligned_df, df[merge_cols]], axis=1, sort=False
                            )
                        except Exception as e2:
                            self.logger.error(
                                f"使用 concat 合并测量点 {measurement_name} 数据也失败: {e2}"
                            )
                            continue

        # 记录处理统计信息
        if processed_measurements > 0:
            self.logger.info(
                f"成功处理 {processed_measurements}/{total_measurements} 个测量点"
            )
            if any(data_type_stats.values()):
                self.logger.info(
                    f"数据类型统计: 数值列={data_type_stats['numeric']}, "
                    f"字符串列={data_type_stats['string']}, 布尔列={data_type_stats['boolean']}, "
                    f"时间列={data_type_stats['datetime']}, 其他列={data_type_stats['other']}"
                )

        # 时间戳重排序
        aligned_df = aligned_df.sort_index()

        # 是否需要重采样数据
        if resample and not aligned_df.empty and time_interval and fill_method:
            self.logger.debug("开始应用重采样和填充，支持混合数据类型")
            aligned_df = self._apply_resample_and_fill(
                aligned_df, time_interval, fill_method
            )

        return aligned_df

    def _apply_resample_and_fill(
        self,
        df: pd.DataFrame,
        time_interval: str,
        fill_method: str,
    ) -> pd.DataFrame:
        """应用重采样和填充，智能处理混合数据类型"""
        if df.empty:
            return df

        # 保存原始列顺序
        original_columns = df.columns.tolist()

        # 分类不同数据类型的列
        numeric_columns = df.select_dtypes(
            include=["int64", "float64", "int32", "float32", "int16", "float16"]
        ).columns.tolist()
        boolean_columns = df.select_dtypes(include=["bool"]).columns.tolist()
        string_columns = df.select_dtypes(include=["object", "string"]).columns.tolist()
        datetime_columns = df.select_dtypes(include=["datetime64"]).columns.tolist()

        # 检查 object 类型列中是否包含布尔值（由于 NaN 导致的类型转换）
        object_boolean_columns = []
        for col in string_columns:
            if df[col].dropna().dtype == "bool" or all(
                isinstance(x, (bool, type(None))) for x in df[col].dropna().unique()
            ):
                object_boolean_columns.append(col)

        # 将 object 类型的布尔列从 string_columns 移到 boolean_columns
        for col in object_boolean_columns:
            string_columns.remove(col)
            boolean_columns.append(col)

        other_columns = [
            col
            for col in df.columns
            if col
            not in numeric_columns + boolean_columns + string_columns + datetime_columns
        ]

        self.logger.debug(
            f"重采样列分类 - 数值列: {len(numeric_columns)}, 布尔列: {len(boolean_columns)}, "
            f"字符串列: {len(string_columns)}, 时间列: {len(datetime_columns)}, 其他列: {len(other_columns)}"
        )

        resampled_parts = {}

        # 处理数值列 - 使用均值聚合
        if numeric_columns:
            numeric_resampled = df[numeric_columns].resample(time_interval).mean()
            for col in numeric_columns:
                resampled_parts[col] = numeric_resampled[col]

        # 处理布尔列 - 使用 first() 保持原始值特性
        if boolean_columns:
            boolean_resampled = df[boolean_columns].resample(time_interval).first()
            for col in boolean_columns:
                resampled_parts[col] = boolean_resampled[col]

        # 处理字符串列 - 使用 first() 保留第一个值
        if string_columns:
            string_resampled = df[string_columns].resample(time_interval).first()
            for col in string_columns:
                resampled_parts[col] = string_resampled[col]

        # 处理时间列 - 使用 first() 保留第一个值
        if datetime_columns:
            datetime_resampled = df[datetime_columns].resample(time_interval).first()
            for col in datetime_columns:
                resampled_parts[col] = datetime_resampled[col]

        # 处理其他类型列 - 使用 first() 保留第一个值
        if other_columns:
            other_resampled = df[other_columns].resample(time_interval).first()
            for col in other_columns:
                resampled_parts[col] = other_resampled[col]

        # 按原始列顺序重新组合 DataFrame
        if resampled_parts:
            resampled_df = pd.DataFrame(index=list(resampled_parts.values())[0].index)
            for col in original_columns:
                if col in resampled_parts:
                    resampled_df[col] = resampled_parts[col]
        else:
            # 如果没有任何列被处理，返回空的 DataFrame 但保持时间索引
            time_index = pd.date_range(
                start=df.index.min(), end=df.index.max(), freq=time_interval
            )
            resampled_df = pd.DataFrame(index=time_index, columns=original_columns)

        # 应用填充策略
        resampled_df = self._apply_fill_strategy(
            resampled_df,
            fill_method,
            numeric_columns,
            boolean_columns,
            string_columns,
            datetime_columns,
            other_columns,
        )

        return resampled_df

    def _apply_fill_strategy(
        self,
        df: pd.DataFrame,
        fill_method: str,
        numeric_columns: list,
        boolean_columns: list,
        string_columns: list,
        datetime_columns: list,
        other_columns: list,
    ) -> pd.DataFrame:
        """根据数据类型应用不同的填充策略"""
        if df.empty:
            return df

        filled_df = df.copy()

        # 处理数值列的填充
        if numeric_columns:
            numeric_df = filled_df[numeric_columns]

            if fill_method == "linear":
                # 线性插值只适用于数值列
                filled_df[numeric_columns] = numeric_df.interpolate(method="linear")
            elif fill_method == "ffill":
                filled_df[numeric_columns] = numeric_df.ffill()
            elif fill_method == "bfill":
                filled_df[numeric_columns] = numeric_df.bfill()
            elif fill_method == "nearest":
                # 最近邻插值只适用于数值列
                filled_df[numeric_columns] = numeric_df.interpolate(method="nearest")

        # 处理非数值列的填充（布尔、字符串、时间、其他类型）
        non_numeric_columns = (
            boolean_columns + string_columns + datetime_columns + other_columns
        )

        if non_numeric_columns:
            if fill_method in ["linear", "nearest"]:
                # 对于不支持插值的列类型，使用前向填充
                self.logger.debug(f"非数值列使用前向填充替代 {fill_method} 方法")
                filled_df[non_numeric_columns] = (
                    filled_df[non_numeric_columns].ffill().infer_objects()
                )
            elif fill_method == "ffill":
                filled_df[non_numeric_columns] = (
                    filled_df[non_numeric_columns].ffill().infer_objects()
                )
            elif fill_method == "bfill":
                filled_df[non_numeric_columns] = (
                    filled_df[non_numeric_columns].bfill().infer_objects()
                )

        # 对于仍然存在的 NaN 值，进行最后的清理
        # 数值列：如果前向填充后仍有 NaN，尝试后向填充
        if numeric_columns:
            remaining_na_numeric = filled_df[numeric_columns].isna().any()
            if remaining_na_numeric.any():
                self.logger.debug("数值列存在剩余 NaN 值，尝试后向填充")
                filled_df[numeric_columns] = filled_df[numeric_columns].bfill()

        # 非数值列：如果前向填充后仍有 NaN，尝试后向填充
        if non_numeric_columns:
            remaining_na_non_numeric = filled_df[non_numeric_columns].isna().any()
            if remaining_na_non_numeric.any():
                self.logger.debug("非数值列存在剩余 NaN 值，尝试后向填充")
                filled_df[non_numeric_columns] = (
                    filled_df[non_numeric_columns].bfill().infer_objects()
                )

        return filled_df

    def _to_dataframe(self, rs):
        """将查询结果转换为DataFrame"""
        if isinstance(rs, list):
            return [self._to_dataframe(r) for r in rs]

        result = {}
        for key, data in rs.items():
            name, tags = key
            if tags is None:
                key = name
            else:
                key = (name, tuple(sorted(tags.items())))

            df = pd.DataFrame(data)
            if "time" in df.columns:
                # 使用format='mixed'让pandas为每个时间自动推断格式
                df.time = pd.to_datetime(df.time, format="mixed")
                df.set_index("time", inplace=True)
                df.index.name = None

            if key in result:
                result[key] = pd.concat([result[key], df]).sort_index()
            else:
                result[key] = df

            result[key].dropna(how="all", axis=1, inplace=True)

        # 如果结果中只有一个表，直接返回该DataFrame
        if len(result) == 1:
            return list(result.values())[0]

        return result

    def close(self):
        """关闭连接"""
        self.conn.close()


if __name__ == "__main__":
    host = "***********"
    port = 8086
    username = "root"
    password = "123456"
    database = "annealing_furnace"

    try:
        influx_query = InfluxQuery(host, port, username, password, database)

        # 使用ISO8601格式的时间字符串
        measurements = ["Luzi.PLC03.DB34,REAL170", "Luzi.PLC03.DB34,REAL174"]
        start_time = datetime.strptime("2025-04-16T00:29:00Z", "%Y-%m-%dT%H:%M:%SZ")
        end_time = datetime.strptime("2025-04-16T00:30:00Z", "%Y-%m-%dT%H:%M:%SZ")

        df = influx_query.batch_query_aligned_df(
            measurements,
            start_time=start_time,
            end_time=end_time,
            time_interval="1s",
            fill_method="linear",
        )
        print(f"查询结果: {df}")

        influx_query.close()
    except Exception as e:
        print(f"执行出错: {e}")
